# AI Answer Bot Architecture

This document outlines the technical architecture of the AI Answer Bot system, including component relationships, data flow, and deployment infrastructure.

## System Overview

The AI Answer Bot is a multi-tenant SaaS solution consisting of three main components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Embed Widget  │    │   Admin Panel   │    │   API Backend   │
│  (React/TS)     │    │  (React/TS)     │    │  (Node.js/TS)   │
│                 │    │                 │    │                 │
│ • Chat Interface│    │ • Client Mgmt   │    │ • REST API      │
│ • Customizable  │    │ • Bot Config    │    │ • PostgreSQL    │
│ • Responsive    │    │ • Analytics     │    │ • AI Integration│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   Database      │
                    │                 │
                    │ • Client Data   │
                    │ • Content Cache │
                    │ • Analytics     │
                    └─────────────────┘
```

## Component Architecture

### 1. Embed Widget (@ai-bot/embed)

**Purpose**: Customer-facing chat interface that embeds into client websites

**Technology Stack**:
- React 18 with TypeScript
- CSS Modules for styling
- Vite for building and bundling
- UMD format for universal compatibility

**Key Features**:
- Floating chat widget with modern design
- Customizable branding (colors, logo, text)
- Smart suggestions based on scraped content
- Link previews with contextual information
- Mobile-responsive design
- Easy integration via script tag

**Integration Methods**:
```javascript
// Method 1: Script tag
<script src="cdn-url/ai-answer-bot.umd.js"></script>
<script>window.AIAnswerBot.init(config);</script>

// Method 2: React component
import { Widget } from '@ai-bot/embed';
<Widget config={config} />
```

### 2. Admin Panel (@ai-bot/admin)

**Purpose**: Management interface for configuring clients and monitoring bot performance

**Technology Stack**:
- React 18 with TypeScript
- Tailwind CSS for styling
- React Router for navigation
- React Query for data fetching
- React Hook Form for form management

**Key Features**:
- Client management (CRUD operations)
- Bot configuration and testing
- Content scraping management
- Analytics dashboard
- Brand customization tools
- Real-time system monitoring

**Pages Structure**:
```
/                 - Dashboard overview
/clients          - Client list and management
/clients/new      - Create new client
/clients/:id      - Client details and configuration
/bot              - Bot management and testing
/analytics        - Usage analytics and reporting
/settings         - System configuration
```

### 3. API Backend (@ai-bot/api)

**Purpose**: Core backend service handling all business logic and data management

**Technology Stack**:
- Node.js with TypeScript
- Express.js framework
- PostgreSQL with connection pooling
- Joi for request validation
- Rate limiting and security middleware

**API Endpoints**:
```
POST /api/bot/ask              - Get AI response
GET  /api/bot/suggestions/:id  - Get suggestions
GET  /api/bot/links/:id        - Get link previews
GET  /api/clients              - List clients (admin)
POST /api/clients              - Create client (admin)
GET  /api/clients/:id          - Get client details
PUT  /api/clients/:id          - Update client
DELETE /api/clients/:id        - Delete client (admin)
POST /api/analytics/track      - Track interaction
GET  /api/analytics/:id        - Get analytics data
GET  /health                   - Health check
```

## Data Flow

### 1. Widget Initialization
```
Website → Load embed script → Initialize widget → Fetch suggestions/links → Display UI
```

### 2. User Interaction
```
User question → Widget → API /bot/ask → AI processing → Response with suggestions/links → Widget display
```

### 3. Admin Management
```
Admin action → Admin panel → API endpoint → Database update → Response → UI update
```

## Database Schema

### Core Tables

**clients**
- Stores client configuration and API keys
- JSONB config field for flexible branding options
- Unique constraints on domain and API key

**scraped_pages**
- Website content for each client
- Full-text search capabilities
- Automatic content freshness tracking

**bot_suggestions**
- Predefined questions with priority ordering
- Category-based organization
- Active/inactive status management

**bot_responses**
- Cached AI responses for performance
- Automatic expiration (24 hours)
- Question hashing for deduplication

**analytics**
- User interaction tracking
- JSONB data field for flexible event storage
- Time-series data for reporting

### Relationships
```sql
clients (1) → (many) scraped_pages
clients (1) → (many) bot_suggestions  
clients (1) → (many) bot_responses
clients (1) → (many) analytics
```

## AWS Infrastructure

### Production Deployment

```
Internet → CloudFront → S3 (Frontend)
Internet → API Gateway → Lambda (API) → RDS (Database)
```

**Components**:
- **Lambda**: Serverless API execution
- **API Gateway**: HTTP API management and routing
- **RDS PostgreSQL**: Managed database with automated backups
- **S3**: Static file hosting for frontend applications
- **CloudFront**: Global CDN with edge caching
- **IAM**: Role-based access control

### Security Architecture

**Network Security**:
- CloudFront-only S3 access (no public bucket access)
- VPC-isolated RDS instance
- API Gateway rate limiting
- CORS configuration for allowed origins

**Authentication**:
- Client API keys for embed widget access
- Admin API keys for management operations
- JWT tokens for session management (future enhancement)

**Data Protection**:
- RDS encryption at rest
- HTTPS/TLS for all communications
- Input validation and sanitization
- SQL injection prevention

## Performance Considerations

### Caching Strategy
- **Bot Responses**: 24-hour cache in database
- **Static Assets**: Long-term browser caching via CloudFront
- **API Responses**: Short-term caching for suggestions/links
- **Database**: Connection pooling and query optimization

### Scalability
- **Lambda**: Auto-scaling based on demand
- **RDS**: Vertical scaling, read replicas for high load
- **CloudFront**: Global edge locations
- **S3**: Unlimited storage capacity

### Monitoring
- **CloudWatch**: Lambda metrics and logs
- **RDS Monitoring**: Database performance metrics
- **Custom Analytics**: User interaction tracking
- **Health Checks**: Automated system status monitoring

## Development Workflow

### Local Development
```bash
# Start all services
npm run dev

# Individual services
npm run dev:api     # API server on :3000
npm run dev:admin   # Admin panel on :5173
npm run dev:embed   # Embed widget on :5174
```

### Testing Strategy
- **Unit Tests**: Jest for API business logic
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Admin panel user flows
- **Widget Testing**: Cross-browser compatibility

### CI/CD Pipeline
1. **Code Push** → GitHub/GitLab
2. **Automated Tests** → Jest, ESLint, TypeScript
3. **Build Artifacts** → Compiled packages
4. **Deploy to Staging** → AWS staging environment
5. **Manual Testing** → QA verification
6. **Deploy to Production** → AWS production environment

## Monitoring and Observability

### Metrics to Track
- **API Performance**: Response times, error rates
- **Database**: Query performance, connection pool usage
- **User Engagement**: Questions asked, suggestions clicked
- **System Health**: Lambda cold starts, RDS connections

### Alerting
- **API Errors**: >5% error rate
- **Database**: Connection failures, slow queries
- **Lambda**: Timeout errors, memory issues
- **CloudFront**: Origin errors, high latency

## Future Enhancements

### Planned Features
- **AI Integration**: OpenAI/Claude API integration
- **Advanced Analytics**: Conversion tracking, A/B testing
- **Multi-language**: Internationalization support
- **Advanced Scraping**: Automated content updates
- **White-label**: Custom domain support

### Technical Improvements
- **Caching**: Redis for improved performance
- **Search**: Elasticsearch for better content matching
- **Real-time**: WebSocket support for live chat
- **Mobile**: React Native app for admin panel
