#!/bin/bash

# Quick fix for PostgreSQL database connection issues
# This script diagnoses and fixes common PostgreSQL connection problems on macOS

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 PostgreSQL Connection Fix"
echo "============================"
echo

# Configuration
DB_NAME="ai_answer_bot"
current_user=$(whoami)

log_info "Current user: $current_user"
log_info "Target database: $DB_NAME"

# Check PostgreSQL status
log_info "Checking PostgreSQL status..."
if brew services list | grep postgresql | grep started > /dev/null; then
    log_success "PostgreSQL service is running"
else
    log_warning "PostgreSQL service not running. Starting..."
    brew services start postgresql@16 || brew services start postgresql@17 || brew services start postgresql
fi

# Check if database exists
log_info "Checking if database exists..."
if psql -lqt | cut -d \| -f 1 | grep -qw $DB_NAME; then
    log_success "Database '$DB_NAME' exists"
else
    log_warning "Database '$DB_NAME' does not exist. Creating..."
    createdb $DB_NAME
    if [ $? -eq 0 ]; then
        log_success "Database '$DB_NAME' created successfully"
    else
        log_error "Failed to create database"
        exit 1
    fi
fi

# Test connection with current user (no password)
log_info "Testing connection with current user..."
if psql -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1; then
    log_success "Connection successful with current user ($current_user)"
    
    # Update .env file to use current user
    if [ -f "packages/api/.env" ]; then
        log_info "Updating .env file to use current user..."
        sed -i.backup "s/DB_USER=.*/DB_USER=$current_user/" packages/api/.env
        sed -i.backup "s/DB_PASSWORD=.*/DB_PASSWORD=/" packages/api/.env
        log_success "Updated packages/api/.env"
    fi
    
    echo
    log_success "✅ Database connection fixed!"
    echo
    echo "Updated configuration:"
    echo "• Database: $DB_NAME"
    echo "• User: $current_user"
    echo "• Password: (none needed)"
    echo
    echo "You can now run: npm run setup-and-run"
    
else
    log_error "Still cannot connect to database"
    echo
    echo "Manual troubleshooting steps:"
    echo "1. Check PostgreSQL is running: brew services list | grep postgresql"
    echo "2. Try connecting manually: psql -d $DB_NAME"
    echo "3. Check PostgreSQL logs: brew services info postgresql"
    echo "4. Restart PostgreSQL: brew services restart postgresql"
fi
