#!/bin/bash

# AI Answer Bot Demo - One-Command Setup and Run Function
# Copy and paste this entire function into your terminal, then run: ai_bot_setup

ai_bot_setup() {
    # Colors for output
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    PURPLE='\033[0;35m'
    CYAN='\033[0;36m'
    NC='\033[0m' # No Color

    # Configuration
    DB_NAME="ai_answer_bot"
    DB_USER="postgres"
    DB_PASSWORD="password"
    DB_HOST="localhost"
    DB_PORT="5432"
    API_PORT="5000"
    ADMIN_PORT="5001"
    EMBED_PORT="5175"

    # Helper functions
    log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
    log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
    log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
    log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
    log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
    log_config() { echo -e "${CYAN}[CONFIG]${NC} $1"; }

    # Generate secure random strings
    generate_secret() {
        openssl rand -hex 32 2>/dev/null || echo "fallback-secret-$(date +%s)-$(shuf -i 1000-9999 -n 1)"
    }

    echo "🚀 AI Answer Bot Demo - One-Command Setup & Run"
    echo "================================================"
    echo

    # Check if we're in the right directory
    if [ ! -f "package.json" ] || ! grep -q "ai-answer-bot-demo" package.json; then
        log_error "Please run this function from the AI Answer Bot Demo root directory"
        return 1
    fi

    # Check PostgreSQL
    log_step "Checking PostgreSQL installation..."
    if ! command -v psql &> /dev/null; then
        log_warning "PostgreSQL not found. Installing PostgreSQL 17..."
        case "$(uname -s)" in
            Darwin*)
                if ! command -v brew &> /dev/null; then
                    log_error "Homebrew required for macOS PostgreSQL installation"
                    log_info "Install Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                    return 1
                fi
                # Stop old versions
                brew services stop postgresql@15 2>/dev/null || true
                brew services stop postgresql@16 2>/dev/null || true
                # Install PostgreSQL 17
                brew install postgresql@17
                brew services start postgresql@17
                export PATH="/opt/homebrew/opt/postgresql@17/bin:$PATH"
                ;;
            Linux*)
                sudo apt-get update && sudo apt-get install -y postgresql postgresql-contrib
                sudo systemctl start postgresql
                ;;
            *)
                log_error "Unsupported OS. Please install PostgreSQL manually."
                return 1
                ;;
        esac
    fi
    log_success "PostgreSQL available"

    # Setup database
    log_step "Setting up database..."
    local current_user=$(whoami)

    if sudo -u postgres psql -c "SELECT 1;" &> /dev/null 2>&1; then
        # Linux setup
        sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
        sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true
        sudo -u postgres createdb -O $DB_USER $DB_NAME 2>/dev/null || true
    else
        # macOS setup - use current user as superuser
        log_info "Setting up on macOS with user: $current_user..."
        createdb $DB_NAME 2>/dev/null || true

        # If we need a specific postgres user, create it
        if [ "$DB_USER" != "$current_user" ]; then
            psql -d postgres -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
            psql -d postgres -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true
            psql -d postgres -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true
        else
            # If DB_USER is the same as current user, no password needed
            DB_PASSWORD=""
        fi
    fi

    # Test database connection
    local connection_success=false

    # Try different connection methods
    if [ "$DB_USER" = "$current_user" ] && [ -z "$DB_PASSWORD" ]; then
        # macOS case: current user, no password
        if psql -h $DB_HOST -p $DB_PORT -d $DB_NAME -c "SELECT 1;" &> /dev/null 2>&1; then
            connection_success=true
        fi
    fi

    # Try with password
    if [ "$connection_success" = false ] && PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null 2>&1; then
        connection_success=true
    fi

    # Try without password (for local development)
    if [ "$connection_success" = false ] && psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null 2>&1; then
        connection_success=true
        DB_PASSWORD=""  # Clear password for .env file
    fi

    if [ "$connection_success" = false ]; then
        log_error "Database connection failed. Please check PostgreSQL setup."
        log_info "Tried connecting as user: $DB_USER to database: $DB_NAME"
        return 1
    fi
    log_success "Database connection successful"

    # Create environment files
    log_step "Creating environment configurations..."

    # Generate secure keys
    local admin_key="dev-admin-key-$(date +%s)"
    local jwt_secret=$(generate_secret)

    # Backup existing files
    [ -f "packages/api/.env" ] && cp "packages/api/.env" "packages/api/.env.backup.$(date +%s)"
    [ -f "packages/admin/.env.local" ] && cp "packages/admin/.env.local" "packages/admin/.env.local.backup.$(date +%s)"
    [ -f "packages/embed/.env.local" ] && cp "packages/embed/.env.local" "packages/embed/.env.local.backup.$(date +%s)"

    # API .env - Complete configuration
    cat > packages/api/.env << EOF
# Database Configuration
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Server Configuration
PORT=$API_PORT
NODE_ENV=development

# Security
ADMIN_API_KEY=$admin_key
JWT_SECRET=$jwt_secret

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5000,http://localhost:$ADMIN_PORT,http://localhost:$EMBED_PORT,http://localhost:5173,http://localhost:5174,http://localhost:5175

# Development flags
DEBUG=true

# AWS Configuration (for deployment)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# Lambda Configuration
LAMBDA_FUNCTION_NAME=ai-answer-bot-api
API_GATEWAY_NAME=ai-answer-bot-gateway

# RDS Configuration (for production)
RDS_INSTANCE_IDENTIFIER=ai-answer-bot-db
RDS_DB_NAME=ai_answer_bot
RDS_USERNAME=postgres
RDS_PASSWORD=your-secure-rds-password

# S3 Configuration (for deployment)
S3_BUCKET_NAME=ai-answer-bot-frontend
CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id

# Rate Limiting
RATE_LIMIT_POINTS=100
RATE_LIMIT_DURATION=60

# Logging
LOG_LEVEL=debug
EOF

    # Admin .env.local - Complete configuration
    cat > packages/admin/.env.local << EOF
# API Configuration
VITE_API_URL=http://localhost:$API_PORT/api
VITE_ADMIN_API_KEY=$admin_key

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SCRAPING=true
VITE_ENABLE_TESTING=true
VITE_ENABLE_DEBUG=true

# External Services (optional - for production)
VITE_SENTRY_DSN=your-sentry-dsn-here
VITE_GOOGLE_ANALYTICS_ID=your-ga-id-here

# Development Settings
VITE_API_TIMEOUT=10000
VITE_REFRESH_INTERVAL=30000
EOF

    # Embed .env.local - New configuration for embed package
    cat > packages/embed/.env.local << EOF
# API Configuration
VITE_API_URL=http://localhost:$API_PORT/api
VITE_DEFAULT_CLIENT_ID=demo-client-id

# Environment
VITE_NODE_ENV=development

# Demo Configuration
VITE_DEMO_PRIMARY_COLOR=#667eea
VITE_DEMO_ASK_TEXT=How can we help?
VITE_DEMO_PLACEHOLDER=Ask us anything about our demo...
VITE_DEMO_SITE_NAME=Demo Website
VITE_DEMO_LOGO=https://via.placeholder.com/40x40/667eea/ffffff?text=D

# Development Settings
VITE_ENABLE_DEBUG=true
VITE_ENABLE_CONSOLE_LOGS=true

# Widget Configuration
VITE_WIDGET_POSITION=bottom-right
VITE_WIDGET_OFFSET_X=20
VITE_WIDGET_OFFSET_Y=20
EOF

    # Update embed demo config to use environment variables
    if [ -f "packages/embed/src/demo.tsx" ]; then
        # Create a backup
        cp "packages/embed/src/demo.tsx" "packages/embed/src/demo.tsx.backup.$(date +%s)"

        # Update the demo configuration to use environment variables
        cat > packages/embed/src/demo.tsx << 'EOF'
/**
 * Demo application for the AI Answer Bot embed widget.
 * This component showcases the widget functionality with a mock website interface
 * and demonstrates responsive behavior across different screen sizes.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import Widget from './components/Widget';
import { BotConfig } from './types';

const Demo: React.FC = () => {
  const demoConfig: BotConfig = {
    clientId: import.meta.env.VITE_DEFAULT_CLIENT_ID || 'b974bfed-a282-40c6-a091-0367a97feb77',
    apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:3004/api',
    primaryColor: import.meta.env.VITE_DEMO_PRIMARY_COLOR || '#667eea',
    askAnythingText: import.meta.env.VITE_DEMO_ASK_TEXT || 'How can we help?',
    logo: import.meta.env.VITE_DEMO_LOGO || 'https://via.placeholder.com/40x40/667eea/ffffff?text=D',
    placeholder: import.meta.env.VITE_DEMO_PLACEHOLDER || 'Ask us anything about our demo...',
    siteName: import.meta.env.VITE_DEMO_SITE_NAME || 'Demo Website'
  };

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Inter, system-ui, sans-serif'
    }}>
      <div style={{
        background: 'white',
        borderRadius: '12px',
        padding: '2rem',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        maxWidth: '500px',
        textAlign: 'center'
      }}>
        <h1 style={{ color: '#1f2937', marginBottom: '1rem' }}>
          AI Answer Bot Demo
        </h1>
        <p style={{ color: '#6b7280', marginBottom: '2rem' }}>
          This is a demonstration of the AI Answer Bot embed widget.
          The widget appears in the bottom-right corner and can be customized
          with your branding and configuration.
        </p>
        <div style={{
          background: '#f3f4f6',
          padding: '1rem',
          borderRadius: '8px',
          fontSize: '0.875rem',
          color: '#374151'
        }}>
          <strong>Environment:</strong> {import.meta.env.VITE_NODE_ENV || 'development'}<br/>
          <strong>API URL:</strong> {demoConfig.apiUrl}<br/>
          <strong>Client ID:</strong> {demoConfig.clientId}
        </div>
      </div>
      <Widget config={demoConfig} />
    </div>
  );
};

// Render the demo
const root = ReactDOM.createRoot(document.getElementById('root')!);
root.render(<Demo />);
EOF
        log_success "Updated embed demo to use environment variables"
    fi

    log_success "Environment files created"
    log_config "✓ API .env: Database, security, CORS, AWS settings"
    log_config "✓ Admin .env.local: API URL, admin key, feature flags"
    log_config "✓ Embed .env.local: API URL, demo configuration, widget settings"
    log_config "✓ Updated embed demo to use environment variables"

    # Install dependencies
    log_step "Installing dependencies..."
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    log_success "Dependencies installed"

    # Run migrations
    log_step "Running database migrations..."
    cd packages/api && npm run migrate && cd ../..
    log_success "Migrations completed"

    # Seed database
    echo
    read -p "Seed database with demo data? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        log_step "Seeding database..."
        cd packages/api && npm run seed && cd ../..
        log_success "Database seeded"
    fi

    # Check ports
    log_step "Checking ports..."
    local ports=($API_PORT $ADMIN_PORT $EMBED_PORT)
    local unavailable=()
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            unavailable+=($port)
        fi
    done
    
    if [ ${#unavailable[@]} -gt 0 ]; then
        log_warning "Ports in use: ${unavailable[*]}"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Setup cancelled"
            return 1
        fi
    fi

    # Final info
    echo
    log_success "🎉 Setup complete! Starting all services..."
    echo
    echo "=== 🌐 SERVICE URLS ==="
    log_config "• API Backend: http://localhost:$API_PORT"
    log_config "• API Health Check: http://localhost:$API_PORT/health"
    log_config "• Admin Panel: http://localhost:$ADMIN_PORT"
    log_config "• Embed Widget Demo: http://localhost:$EMBED_PORT"
    echo
    echo "=== 🔑 GENERATED CREDENTIALS ==="
    log_config "• Admin API Key: $admin_key"
    log_config "• JWT Secret: ${jwt_secret:0:20}... (truncated)"
    echo
    echo "=== 📁 ENVIRONMENT FILES CREATED ==="
    log_config "• packages/api/.env (Database, API, AWS config)"
    log_config "• packages/admin/.env.local (Admin panel config)"
    log_config "• packages/embed/.env.local (Widget demo config)"
    echo
    log_info "Press Ctrl+C to stop all services"
    echo

    # Start all services
    npx concurrently \
        --names "API,ADMIN,EMBED" \
        --colors "blue,green,yellow" \
        --prefix-colors "blue,green,yellow" \
        --kill-others-on-fail \
        "npm run dev:api" \
        "npm run dev:admin" \
        "npm run dev:embed"
}

# Instructions
echo "🚀 AI Answer Bot Demo - Complete Environment Setup"
echo "=================================================="
echo
echo "This function creates comprehensive .env files for all packages:"
echo "• API: Database, security, CORS, AWS deployment settings"
echo "• Admin: API URLs, admin keys, feature flags, external services"
echo "• Embed: Demo configuration, widget settings, environment variables"
echo
echo "USAGE OPTIONS:"
echo "1. Copy this entire file and paste into terminal, then run: ai_bot_setup"
echo "2. Or run: source setup-and-run-function.sh && ai_bot_setup"
echo "3. Or use npm script: npm run setup-and-run"
echo
echo "WHAT IT DOES:"
echo "✓ Installs PostgreSQL (if missing)"
echo "✓ Creates database and user"
echo "✓ Generates secure API keys and JWT secrets"
echo "✓ Creates complete .env files for all packages"
echo "✓ Updates Vite configs with correct ports"
echo "✓ Installs dependencies"
echo "✓ Runs database migrations"
echo "✓ Seeds demo data (optional)"
echo "✓ Starts all services simultaneously"
echo
echo "PORTS USED:"
echo "• API Backend: 5000"
echo "• Admin Panel: 5001"
echo "• Embed Widget: 5175"
