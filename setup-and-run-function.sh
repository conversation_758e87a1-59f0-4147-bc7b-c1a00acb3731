#!/bin/bash

# AI Answer Bot Demo - One-Command Setup and Run Function
# Copy and paste this entire function into your terminal, then run: ai_bot_setup

ai_bot_setup() {
    # Colors for output
    RED='\033[0;31m'
    GREEN='\033[0;32m'
    YELLOW='\033[1;33m'
    BLUE='\033[0;34m'
    PURPLE='\033[0;35m'
    CYAN='\033[0;36m'
    NC='\033[0m' # No Color

    # Configuration
    DB_NAME="ai_answer_bot"
    DB_USER="postgres"
    DB_PASSWORD="password"
    DB_HOST="localhost"
    DB_PORT="5432"
    API_PORT="3004"
    ADMIN_PORT="5174"
    EMBED_PORT="5175"

    # Helper functions
    log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
    log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
    log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
    log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
    log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }
    log_config() { echo -e "${CYAN}[CONFIG]${NC} $1"; }

    # Generate secure random strings
    generate_secret() {
        openssl rand -hex 32 2>/dev/null || echo "fallback-secret-$(date +%s)-$(shuf -i 1000-9999 -n 1)"
    }

    echo "🚀 AI Answer Bot Demo - One-Command Setup & Run"
    echo "================================================"
    echo

    # Check if we're in the right directory
    if [ ! -f "package.json" ] || ! grep -q "ai-answer-bot-demo" package.json; then
        log_error "Please run this function from the AI Answer Bot Demo root directory"
        return 1
    fi

    # Check PostgreSQL
    log_step "Checking PostgreSQL installation..."
    if ! command -v psql &> /dev/null; then
        log_warning "PostgreSQL not found. Installing..."
        case "$(uname -s)" in
            Darwin*)
                if ! command -v brew &> /dev/null; then
                    log_error "Homebrew required for macOS PostgreSQL installation"
                    log_info "Install Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                    return 1
                fi
                brew install postgresql@15
                brew services start postgresql@15
                export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
                ;;
            Linux*)
                sudo apt-get update && sudo apt-get install -y postgresql postgresql-contrib
                sudo systemctl start postgresql
                ;;
            *)
                log_error "Unsupported OS. Please install PostgreSQL manually."
                return 1
                ;;
        esac
    fi
    log_success "PostgreSQL available"

    # Setup database
    log_step "Setting up database..."
    if sudo -u postgres psql -c "SELECT 1;" &> /dev/null 2>&1; then
        # Linux setup
        sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
        sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true
        sudo -u postgres createdb -O $DB_USER $DB_NAME 2>/dev/null || true
    else
        # macOS setup
        createdb $DB_NAME 2>/dev/null || true
    fi

    # Test database connection
    if ! PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null 2>&1; then
        log_error "Database connection failed. Please check PostgreSQL setup."
        return 1
    fi
    log_success "Database connection successful"

    # Create environment files
    log_step "Creating environment configurations..."
    
    # API .env
    local admin_key="dev-admin-key-$(date +%s)"
    local jwt_secret=$(generate_secret)
    
    cat > packages/api/.env << EOF
# Database Configuration
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Server Configuration
PORT=$API_PORT
NODE_ENV=development

# Security
ADMIN_API_KEY=$admin_key
JWT_SECRET=$jwt_secret

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:$ADMIN_PORT,http://localhost:$EMBED_PORT,http://localhost:5173

# Development flags
DEBUG=true
EOF

    # Admin .env.local
    cat > packages/admin/.env.local << EOF
# API Configuration
VITE_API_URL=http://localhost:$API_PORT/api
VITE_ADMIN_API_KEY=$admin_key

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SCRAPING=true
VITE_ENABLE_TESTING=true
EOF

    # Update embed demo config
    if [ -f "packages/embed/src/demo.tsx" ]; then
        sed -i.backup "s|apiUrl: 'http://localhost:3004/api'|apiUrl: 'http://localhost:$API_PORT/api'|g" packages/embed/src/demo.tsx 2>/dev/null || true
    fi

    log_success "Environment files created"

    # Install dependencies
    log_step "Installing dependencies..."
    if [ ! -d "node_modules" ]; then
        npm install
    fi
    log_success "Dependencies installed"

    # Run migrations
    log_step "Running database migrations..."
    cd packages/api && npm run migrate && cd ../..
    log_success "Migrations completed"

    # Seed database
    echo
    read -p "Seed database with demo data? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        log_step "Seeding database..."
        cd packages/api && npm run seed && cd ../..
        log_success "Database seeded"
    fi

    # Check ports
    log_step "Checking ports..."
    local ports=($API_PORT $ADMIN_PORT $EMBED_PORT)
    local unavailable=()
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            unavailable+=($port)
        fi
    done
    
    if [ ${#unavailable[@]} -gt 0 ]; then
        log_warning "Ports in use: ${unavailable[*]}"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Setup cancelled"
            return 1
        fi
    fi

    # Final info
    echo
    log_success "🎉 Setup complete! Starting all services..."
    echo
    log_config "• API Backend: http://localhost:$API_PORT"
    log_config "• Admin Panel: http://localhost:$ADMIN_PORT" 
    log_config "• Embed Widget: http://localhost:$EMBED_PORT"
    echo
    log_info "Press Ctrl+C to stop all services"
    echo

    # Start all services
    npx concurrently \
        --names "API,ADMIN,EMBED" \
        --colors "blue,green,yellow" \
        --prefix-colors "blue,green,yellow" \
        --kill-others-on-fail \
        "npm run dev:api" \
        "npm run dev:admin" \
        "npm run dev:embed"
}

# Instructions
echo "🚀 AI Answer Bot Demo - One-Command Setup"
echo "=========================================="
echo
echo "To use this function:"
echo "1. Copy this entire file content"
echo "2. Paste it into your terminal"
echo "3. Run: ai_bot_setup"
echo
echo "Or simply run: source setup-and-run-function.sh && ai_bot_setup"
