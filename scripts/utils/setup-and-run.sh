#!/bin/bash

# AI Answer Bot Demo - Complete Setup and Run Script
# This script sets up all environment variables, configures the database, and runs all components simultaneously

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
DB_NAME="ai_answer_bot"
DB_USER="postgres"
DB_PASSWORD="password"
DB_HOST="localhost"
DB_PORT="5432"
API_PORT="3004"
ADMIN_PORT="5174"
EMBED_PORT="5175"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_config() {
    echo -e "${CYAN}[CONFIG]${NC} $1"
}

# Function to check if PostgreSQL is installed
check_postgresql() {
    log_info "Checking PostgreSQL installation..."
    
    if command -v psql &> /dev/null; then
        local version=$(psql --version | head -n1)
        log_success "PostgreSQL found: $version"
        return 0
    else
        log_warning "PostgreSQL not found"
        return 1
    fi
}

# Function to install PostgreSQL (macOS with Homebrew)
install_postgresql_mac() {
    log_info "Installing PostgreSQL on macOS..."
    
    if ! command -v brew &> /dev/null; then
        log_error "Homebrew not found. Please install Homebrew first:"
        echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        return 1
    fi
    
    brew install postgresql@15
    brew services start postgresql@15
    
    # Add to PATH
    echo 'export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"' >> ~/.zshrc
    export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
    
    log_success "PostgreSQL installed and started"
}

# Function to install PostgreSQL (Linux)
install_postgresql_linux() {
    log_info "Installing PostgreSQL on Linux..."
    
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        sudo apt-get update
        sudo apt-get install -y postgresql postgresql-contrib
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        sudo yum install -y postgresql-server postgresql-contrib
        sudo postgresql-setup initdb
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    else
        log_error "Unsupported Linux distribution. Please install PostgreSQL manually."
        return 1
    fi
    
    log_success "PostgreSQL installed and started"
}

# Function to setup PostgreSQL user and database
setup_database() {
    log_step "Setting up database and user..."
    
    # Check if we can connect as postgres user
    if sudo -u postgres psql -c "SELECT 1;" &> /dev/null; then
        # Linux setup
        log_info "Setting up on Linux..."
        sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
        sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true
        sudo -u postgres createdb -O $DB_USER $DB_NAME 2>/dev/null || true
    else
        # macOS setup (current user is usually postgres)
        log_info "Setting up on macOS..."
        createdb $DB_NAME 2>/dev/null || true
    fi
    
    log_success "Database setup completed"
}

# Function to test database connection
test_connection() {
    log_info "Testing database connection..."
    
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        log_success "Database connection successful"
        return 0
    else
        log_error "Failed to connect to database"
        return 1
    fi
}

# Function to generate secure random strings
generate_secret() {
    openssl rand -hex 32 2>/dev/null || echo "fallback-secret-$(date +%s)-$(shuf -i 1000-9999 -n 1)"
}

# Function to create API .env file
create_api_env() {
    log_step "Creating API environment configuration..."
    
    local env_file="packages/api/.env"
    
    if [ -f "$env_file" ]; then
        log_warning "API .env file already exists, backing up to .env.backup"
        cp "$env_file" "$env_file.backup"
    fi
    
    local admin_key="dev-admin-key-$(date +%s)"
    local jwt_secret=$(generate_secret)
    
    cat > "$env_file" << EOF
# Database Configuration
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Server Configuration
PORT=$API_PORT
NODE_ENV=development

# Security
ADMIN_API_KEY=$admin_key
JWT_SECRET=$jwt_secret

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:$ADMIN_PORT,http://localhost:$EMBED_PORT,http://localhost:5173

# Development flags
DEBUG=true

# AWS Configuration (for deployment)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Lambda Configuration
LAMBDA_FUNCTION_NAME=ai-answer-bot-api
API_GATEWAY_NAME=ai-answer-bot-gateway

# RDS Configuration
RDS_INSTANCE_IDENTIFIER=ai-answer-bot-db
RDS_DB_NAME=ai_answer_bot
RDS_USERNAME=postgres
RDS_PASSWORD=your-secure-password

# S3 Configuration
S3_BUCKET_NAME=ai-answer-bot-frontend
CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id
EOF
    
    log_success "Created API .env file: $env_file"
    log_config "API will run on port $API_PORT"
    log_config "Admin API Key: $admin_key"
    
    # Return the admin key for use in other configs
    echo "$admin_key"
}

# Function to create Admin .env file
create_admin_env() {
    local admin_key=$1
    log_step "Creating Admin Panel environment configuration..."
    
    local env_file="packages/admin/.env.local"
    
    if [ -f "$env_file" ]; then
        log_warning "Admin .env.local file already exists, backing up to .env.local.backup"
        cp "$env_file" "$env_file.local.backup"
    fi
    
    cat > "$env_file" << EOF
# API Configuration
VITE_API_URL=http://localhost:$API_PORT/api
VITE_ADMIN_API_KEY=$admin_key

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SCRAPING=true
VITE_ENABLE_TESTING=true

# External Services
VITE_SENTRY_DSN=your-sentry-dsn-here
VITE_GOOGLE_ANALYTICS_ID=your-ga-id-here
EOF
    
    log_success "Created Admin .env.local file: $env_file"
    log_config "Admin Panel will run on port $ADMIN_PORT"
}

# Function to create Embed .env file (if needed)
create_embed_env() {
    log_step "Configuring Embed Widget environment..."
    
    # The embed widget gets its config from the demo.tsx file and doesn't need a separate .env
    # But we'll update the demo config to use the correct API URL
    local demo_file="packages/embed/src/demo.tsx"
    
    if [ -f "$demo_file" ]; then
        # Update the API URL in the demo file
        sed -i.backup "s|apiUrl: 'http://localhost:3004/api'|apiUrl: 'http://localhost:$API_PORT/api'|g" "$demo_file" 2>/dev/null || true
        log_success "Updated embed demo configuration"
    fi
    
    log_config "Embed Widget will run on port $EMBED_PORT"
}

# Function to run migrations
run_migrations() {
    log_step "Running database migrations..."

    cd packages/api
    npm run migrate
    cd ../..

    log_success "Database migrations completed"
}

# Function to seed database
seed_database() {
    log_step "Seeding database with demo data..."

    cd packages/api
    npm run seed
    cd ../..

    log_success "Database seeded with demo data"
}

# Function to check if ports are available
check_ports() {
    log_info "Checking if required ports are available..."

    local ports=($API_PORT $ADMIN_PORT $EMBED_PORT)
    local unavailable_ports=()

    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            unavailable_ports+=($port)
        fi
    done

    if [ ${#unavailable_ports[@]} -gt 0 ]; then
        log_warning "The following ports are already in use: ${unavailable_ports[*]}"
        log_info "You may need to stop other services or change port configurations"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Setup cancelled by user"
            exit 1
        fi
    else
        log_success "All required ports are available"
    fi
}

# Function to install dependencies
install_dependencies() {
    log_step "Installing project dependencies..."

    if [ ! -d "node_modules" ]; then
        log_info "Installing root dependencies..."
        npm install
    else
        log_info "Root dependencies already installed"
    fi

    log_success "Dependencies installation completed"
}

# Function to start all services
start_all_services() {
    log_step "Starting all development services..."

    log_info "This will start:"
    log_config "• API Backend on http://localhost:$API_PORT"
    log_config "• Admin Panel on http://localhost:$ADMIN_PORT"
    log_config "• Embed Widget Demo on http://localhost:$EMBED_PORT"

    echo
    log_info "Press Ctrl+C to stop all services"
    echo

    # Use concurrently to run all services
    npx concurrently \
        --names "API,ADMIN,EMBED" \
        --colors "blue,green,yellow" \
        --prefix-colors "blue,green,yellow" \
        --kill-others-on-fail \
        "npm run dev:api" \
        "npm run dev:admin" \
        "npm run dev:embed"
}

# Function to display final information
show_final_info() {
    echo
    log_success "🎉 AI Answer Bot Demo is now running!"
    echo
    echo "=== ACCESS URLS ==="
    echo "• API Health Check: http://localhost:$API_PORT/health"
    echo "• Admin Panel: http://localhost:$ADMIN_PORT"
    echo "• Embed Widget Demo: http://localhost:$EMBED_PORT"
    echo
    echo "=== USEFUL COMMANDS ==="
    echo "• Stop all services: Ctrl+C"
    echo "• Restart setup: npm run setup-and-run"
    echo "• Database reset: npm run db:reset"
    echo "• View logs: Check the terminal output above"
    echo
    echo "=== NEXT STEPS ==="
    echo "1. Visit the Admin Panel to configure clients"
    echo "2. Test the Embed Widget functionality"
    echo "3. Check the API health endpoint"
    echo
}

# Main setup and run function
setup_and_run_all() {
    echo "🚀 AI Answer Bot Demo - Complete Setup & Run"
    echo "=============================================="
    echo

    # Check if PostgreSQL is installed
    if ! check_postgresql; then
        log_info "PostgreSQL not found. Installing..."

        case "$(uname -s)" in
            Darwin*)
                install_postgresql_mac || exit 1
                ;;
            Linux*)
                install_postgresql_linux || exit 1
                ;;
            *)
                log_error "Unsupported operating system. Please install PostgreSQL manually."
                exit 1
                ;;
        esac
    fi

    # Setup database
    setup_database

    # Test connection
    if ! test_connection; then
        log_error "Database setup failed. Please check your PostgreSQL installation."
        exit 1
    fi

    # Create environment files
    local admin_key=$(create_api_env)
    create_admin_env "$admin_key"
    create_embed_env

    # Install dependencies
    install_dependencies

    # Run migrations
    run_migrations

    # Seed database (optional)
    read -p "Would you like to seed the database with demo data? (Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        seed_database
    fi

    # Check ports
    check_ports

    # Show final info
    show_final_info

    # Start all services
    start_all_services
}

# Export the function so it can be called from npm scripts
export -f setup_and_run_all
