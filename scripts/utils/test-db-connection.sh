#!/bin/bash

# Test Database Connection Script
# Quick script to test if the local database is properly configured

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_NAME="ai_answer_bot"
DB_USER="postgres"
DB_PASSWORD="password"
DB_HOST="localhost"
DB_PORT="5432"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test database connection
test_connection() {
    log_info "Testing database connection..."
    
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        log_success "Database connection successful"
        return 0
    else
        log_error "Failed to connect to database"
        return 1
    fi
}

# Check if tables exist
check_tables() {
    log_info "Checking database tables..."
    
    local tables=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT COUNT(*) FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    " 2>/dev/null | tr -d ' ')
    
    if [ "$tables" -gt 0 ]; then
        log_success "Found $tables tables in database"
        
        # List tables
        echo "Tables:"
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        " 2>/dev/null
        
        return 0
    else
        log_warning "No tables found. Run 'npm run db:migrate' to create tables."
        return 1
    fi
}

# Check if demo data exists
check_demo_data() {
    log_info "Checking for demo data..."
    
    local client_count=$(PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "
        SELECT COUNT(*) FROM clients;
    " 2>/dev/null | tr -d ' ')
    
    if [ "$client_count" -gt 0 ]; then
        log_success "Found $client_count demo clients"
        
        # Show client info
        echo "Demo clients:"
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
            SELECT name, domain, LEFT(api_key, 20) || '...' as api_key_preview 
            FROM clients ORDER BY name;
        " 2>/dev/null
        
        return 0
    else
        log_warning "No demo data found. Run 'npm run db:seed' to add demo data."
        return 1
    fi
}

# Test API connection
test_api() {
    log_info "Testing API server connection..."
    
    if curl -s http://localhost:3004/health > /dev/null 2>&1; then
        log_success "API server is running"
        
        # Show health check response
        echo "Health check response:"
        curl -s http://localhost:3004/health | jq . 2>/dev/null || curl -s http://localhost:3004/health
        echo
        
        return 0
    else
        log_warning "API server not running. Start with 'npm run dev:api'"
        return 1
    fi
}

# Main function
main() {
    echo "🔍 AI Answer Bot - Database Connection Test"
    echo "=========================================="
    echo
    
    local all_good=true
    
    # Test database connection
    if ! test_connection; then
        all_good=false
        echo
        log_error "Database connection failed. Try:"
        echo "  1. npm run db:setup    # Full setup"
        echo "  2. npm run db:reset    # Reset existing database"
        echo
    fi
    
    # Check tables
    if ! check_tables; then
        all_good=false
    fi
    
    # Check demo data
    if ! check_demo_data; then
        # This is just a warning, not a failure
        echo
    fi
    
    # Test API
    echo
    if ! test_api; then
        # This is just a warning, not a failure
        echo
    fi
    
    echo
    if [ "$all_good" = true ]; then
        log_success "🎉 All database checks passed!"
        echo
        echo "=== READY TO DEVELOP ==="
        echo "Database: ✅ Connected and configured"
        echo "Tables: ✅ Created and ready"
        echo "Demo data: Available"
        echo
        echo "Start development with: npm run dev"
    else
        log_error "❌ Some checks failed. See messages above for solutions."
        exit 1
    fi
}

# Run main function
main "$@"
