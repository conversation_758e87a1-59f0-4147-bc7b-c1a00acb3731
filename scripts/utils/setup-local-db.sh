#!/bin/bash

# Local Database Setup Script for AI Answer Bot Demo
# This script sets up PostgreSQL locally, creates the database, runs migrations, and seeds data

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_NAME="ai_answer_bot"
DB_USER="postgres"
DB_PASSWORD="password"
DB_HOST="localhost"
DB_PORT="5432"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL is installed
check_postgresql() {
    log_info "Checking PostgreSQL installation..."
    
    if command -v psql &> /dev/null; then
        local version=$(psql --version | head -n1)
        log_success "PostgreSQL found: $version"
        return 0
    else
        log_warning "PostgreSQL not found"
        return 1
    fi
}

# Function to install PostgreSQL (macOS with Homebrew)
install_postgresql_mac() {
    log_info "Installing PostgreSQL on macOS..."
    
    if ! command -v brew &> /dev/null; then
        log_error "Homebrew not found. Please install Homebrew first:"
        echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        return 1
    fi
    
    brew install postgresql@15
    brew services start postgresql@15
    
    # Add to PATH
    echo 'export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"' >> ~/.zshrc
    export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"
    
    log_success "PostgreSQL installed and started"
}

# Function to install PostgreSQL (Linux)
install_postgresql_linux() {
    log_info "Installing PostgreSQL on Linux..."
    
    if command -v apt-get &> /dev/null; then
        # Ubuntu/Debian
        sudo apt-get update
        sudo apt-get install -y postgresql postgresql-contrib
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    elif command -v yum &> /dev/null; then
        # CentOS/RHEL
        sudo yum install -y postgresql-server postgresql-contrib
        sudo postgresql-setup initdb
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    else
        log_error "Unsupported Linux distribution. Please install PostgreSQL manually."
        return 1
    fi
    
    log_success "PostgreSQL installed and started"
}

# Function to setup PostgreSQL user and database
setup_database() {
    log_info "Setting up database and user..."
    
    # Check if we can connect as postgres user
    if sudo -u postgres psql -c "SELECT 1;" &> /dev/null; then
        # Linux setup
        log_info "Setting up on Linux..."
        sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
        sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true
        sudo -u postgres createdb -O $DB_USER $DB_NAME 2>/dev/null || true
    else
        # macOS setup (current user is usually postgres)
        log_info "Setting up on macOS..."
        createdb $DB_NAME 2>/dev/null || true
    fi
    
    log_success "Database setup completed"
}

# Function to test database connection
test_connection() {
    log_info "Testing database connection..."
    
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        log_success "Database connection successful"
        return 0
    else
        log_error "Failed to connect to database"
        return 1
    fi
}

# Function to create .env file
create_env_file() {
    log_info "Creating .env file for API package..."
    
    local env_file="packages/api/.env"
    
    if [ -f "$env_file" ]; then
        log_warning ".env file already exists, backing up to .env.backup"
        cp "$env_file" "$env_file.backup"
    fi
    
    cat > "$env_file" << EOF
# Database Configuration
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASSWORD

# Server Configuration
PORT=3004
NODE_ENV=development

# Security
ADMIN_API_KEY=dev-admin-key-$(date +%s)
JWT_SECRET=dev-jwt-secret-$(openssl rand -hex 32)

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5174,http://localhost:5175

# Development flags
DEBUG=true
EOF
    
    log_success "Created .env file: $env_file"
}

# Function to run migrations
run_migrations() {
    log_info "Running database migrations..."
    
    cd packages/api
    npm run migrate
    cd ../..
    
    log_success "Database migrations completed"
}

# Function to seed database
seed_database() {
    log_info "Seeding database with demo data..."
    
    cd packages/api
    npm run seed
    cd ../..
    
    log_success "Database seeded with demo data"
}

# Main setup function
main() {
    echo "🚀 AI Answer Bot - Local Database Setup"
    echo "======================================"
    echo
    
    # Check if PostgreSQL is installed
    if ! check_postgresql; then
        log_info "PostgreSQL not found. Installing..."
        
        case "$(uname -s)" in
            Darwin*)
                install_postgresql_mac || exit 1
                ;;
            Linux*)
                install_postgresql_linux || exit 1
                ;;
            *)
                log_error "Unsupported operating system. Please install PostgreSQL manually."
                exit 1
                ;;
        esac
    fi
    
    # Setup database
    setup_database
    
    # Test connection
    if ! test_connection; then
        log_error "Database setup failed. Please check your PostgreSQL installation."
        exit 1
    fi
    
    # Create .env file
    create_env_file
    
    # Run migrations
    run_migrations
    
    # Seed database
    read -p "Would you like to seed the database with demo data? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        seed_database
    fi
    
    echo
    log_success "🎉 Local database setup completed!"
    echo
    echo "=== DATABASE INFORMATION ==="
    echo "Database Name: $DB_NAME"
    echo "Host: $DB_HOST"
    echo "Port: $DB_PORT"
    echo "User: $DB_USER"
    echo
    echo "=== NEXT STEPS ==="
    echo "1. Start the API server: npm run dev:api"
    echo "2. Visit health check: http://localhost:3004/health"
    echo "3. Start frontend: npm run dev:admin"
    echo
    echo "=== USEFUL COMMANDS ==="
    echo "Connect to database: psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
    echo "Reset database: DROP DATABASE $DB_NAME; CREATE DATABASE $DB_NAME;"
    echo "Re-run migrations: npm run db:migrate"
    echo "Re-seed data: npm run db:seed"
}

# Run main function
main "$@"
