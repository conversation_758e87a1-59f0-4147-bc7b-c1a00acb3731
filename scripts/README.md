# Deployment Scripts

This directory contains AWS deployment scripts for the AI Answer Bot Demo application.

## 🚀 Quick Start

### Local Development
```bash
# 1. Setup local database (first time only)
npm run db:setup

# 2. Start all services
npm run dev

# Or start individually:
npm run dev:api      # API backend on port 3004
npm run dev:admin    # Admin panel on port 5174
npm run dev:embed    # Embed widget on port 5175
```

### AWS Deployment
```bash
# Deploy everything
npm run deploy:full

# Deploy frontend only (admin panel + embed widget)
npm run deploy:frontend

# Deploy backend only (API)
npm run deploy:backend

# Check status
npm run status:all

# Quick updates (faster than full deployment)
npm run deploy:frontend:update
npm run deploy:backend:update
```

## 📁 Directory Structure

```
scripts/
├── README.md                           # This file
├── deployment/                         # Main deployment scripts
│   ├── README.md                       # Deployment documentation
│   ├── frontend/                       # Frontend deployment scripts
│   │   ├── README.md                   # Frontend documentation
│   │   ├── deploy-frontend.sh          # Full frontend deployment
│   │   ├── update-frontend.sh          # Quick frontend updates
│   │   └── frontend-status.sh          # Frontend status check
│   ├── backend/                        # Backend deployment scripts
│   │   ├── README.md                   # Backend documentation
│   │   ├── deploy-backend.sh           # Full backend deployment
│   │   ├── deploy-api-gateway.sh       # API Gateway deployment
│   │   ├── update-backend.sh           # Quick backend updates
│   │   └── backend-status.sh           # Backend status check
│   └── shared/                         # Shared utilities
│       ├── README.md                   # Shared utilities documentation
│       ├── global.sh                   # Common functions
│       └── config.sh                   # Configuration variables
├── connect/                            # Backend connection utilities
│   └── backend/                        # Backend connection scripts
│       ├── connect-backend.sh          # Connect to deployed backend
│       ├── tail-lambda-logs.sh         # Tail Lambda logs
│       └── update-lambda-env.sh        # Update Lambda environment
└── utils/                              # General utility scripts
    ├── switch-api.sh                   # Switch between local/deployed API
    └── tail-logs.sh                    # Wrapper for log tailing
```

## 🔄 Clean Organization

**Scripts are now properly organized!** The new structure provides:

- ✅ **Better Organization**: Scripts grouped by function (deployment/connect/utils)
- ✅ **Comprehensive Documentation**: README files for each component
- ✅ **Shared Utilities**: Common functions in `global.sh` eliminate code duplication
- ✅ **No Legacy Scripts**: Clean structure without backward compatibility wrappers

### Script Locations

| Function | Script Path | npm Command |
|----------|-------------|-------------|
| **Deployment** |
| Frontend deployment | `scripts/deployment/frontend/deploy-frontend.sh` | `npm run deploy:frontend` |
| Backend deployment | `scripts/deployment/backend/deploy-backend.sh` | `npm run deploy:backend` |
| Full deployment | - | `npm run deploy:full` |
| **Updates** |
| Frontend update | `scripts/deployment/frontend/update-frontend.sh` | `npm run deploy:frontend:update` |
| Backend update | `scripts/deployment/backend/update-backend.sh` | `npm run deploy:backend:update` |
| **Status** |
| Frontend status | `scripts/deployment/frontend/frontend-status.sh` | `npm run deploy:frontend:status` |
| Backend status | `scripts/deployment/backend/backend-status.sh` | `npm run deploy:backend:status` |
| All status | - | `npm run status:all` |
| **Utilities** |
| Switch API endpoint | `scripts/utils/switch-api.sh` | `npm run switch:api` |
| Tail Lambda logs | `scripts/connect/backend/tail-lambda-logs.sh` | `npm run logs:tail` |
| Connect to backend | `scripts/connect/backend/connect-backend.sh` | `npm run backend:connect` |

## 📖 Documentation

Each directory contains detailed documentation:

- **[deployment/README.md](deployment/README.md)** - Overall deployment architecture
- **[deployment/frontend/README.md](deployment/frontend/README.md)** - Frontend deployment details
- **[deployment/backend/README.md](deployment/backend/README.md)** - Backend deployment details  
- **[deployment/shared/README.md](deployment/shared/README.md)** - Shared utilities documentation

## 🛠️ Available Commands

All npm scripts have been updated for the AI Answer Bot Demo project:

```bash
# Frontend (Admin Panel + Embed Widget)
npm run deploy:frontend         # Full frontend deployment to S3/CloudFront
npm run deploy:frontend:update  # Quick frontend updates
npm run deploy:frontend:status  # Frontend status check

# Backend (API)
npm run deploy:backend          # Full backend deployment to Lambda/API Gateway
npm run deploy:backend:update   # Quick backend updates
npm run deploy:backend:status   # Backend status check

# Combined
npm run deploy:full            # Deploy both frontend and backend
npm run status:all             # Check status of all deployments

# Database Setup
npm run db:setup                            # Complete database setup (install + migrate + seed)
npm run db:reset                            # Reset existing database
npm run db:migrate                          # Run database migrations only
npm run db:seed                             # Seed database with demo data only

# Utilities
npm run switch:api [local|deployed|status]  # Switch API endpoints
npm run logs:tail                           # Tail Lambda logs
npm run backend:connect                     # Connect to deployed backend
npm run lambda:env                          # Update Lambda environment variables
```

## 🏗️ Project Structure

This is a monorepo with the following packages:

- **`packages/admin`** - React admin panel with Tailwind CSS
- **`packages/embed`** - React embed widget for website integration
- **`packages/api`** - Node.js/TypeScript backend with Express and PostgreSQL

### Deployment Architecture

- **Frontend**: Both admin and embed packages are built and deployed to S3 with CloudFront
  - Admin panel: `https://your-domain.com/admin/`
  - Embed widget: `https://your-domain.com/embed/`
- **Backend**: API package is deployed as AWS Lambda with API Gateway

## 🔧 Key Improvements

1. **Organized Structure**: Scripts logically grouped by function
2. **Shared Utilities**: Common functions in `global.sh` eliminate ~200 lines of duplicate code
3. **Better Documentation**: Comprehensive README files for each component
4. **Backward Compatibility**: Old script paths still work
5. **Consistent Error Handling**: Standardized across all scripts
6. **Easier Maintenance**: Single source of truth for common functionality

## 🎯 Best Practices

**Use npm scripts** instead of calling scripts directly:

```bash
# ✅ Recommended - uses npm scripts with credential setup
npm run deploy:frontend
npm run deploy:backend
npm run status:all

# ❌ Not recommended - direct script calls
./scripts/deployment/frontend/deploy-frontend.sh
```

**Why use npm scripts?**
- Automatic AWS credential setup (`npm run set:credentials`)
- Consistent execution environment
- Easier to remember and use
- Integrated with the project workflow

## ⚙️ Configuration

Before deploying, update the configuration in `scripts/deployment/shared/config.sh`:

```bash
# Default configuration (already set for bloodandtreasure.com)
export BUCKET_NAME="answer-bot.bloodandtreasure.com"
export LAMBDA_FUNCTION_NAME="answer-bot-bloodandtreasure-api"
export API_GATEWAY_NAME="answer-bot-bloodandtreasure-api-gateway"

# Optional: Customize other settings
export AWS_REGION="us-east-1"
export CORS_ORIGINS="https://*.cloudfront.net,https://bloodandtreasure.com,https://*.bloodandtreasure.com,http://localhost:5173,http://localhost:3000,http://localhost:8080"
```

**Note**: The configuration is already set up for the `bloodandtreasure.com` domain. You can modify these values in `scripts/deployment/shared/config.sh` if needed.

### Prerequisites

1. **AWS CLI** installed and configured
2. **Node.js 18+** and npm
3. **jq** for JSON processing
4. AWS credentials with appropriate permissions for:
   - S3 (create buckets, upload files)
   - CloudFront (create distributions)
   - Lambda (create/update functions)
   - API Gateway (create/update APIs)
   - IAM (create roles and policies)
