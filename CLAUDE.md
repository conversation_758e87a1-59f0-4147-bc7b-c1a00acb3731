# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an AI-powered answer bot system consisting of a monorepo with three main packages:
- **@ai-bot/embed** - React TypeScript embed widget for websites
- **@ai-bot/admin** - React TypeScript admin panel for client management  
- **@ai-bot/api** - Node.js TypeScript API backend with PostgreSQL

The system allows businesses to deploy intelligent chatbots on their websites with scraped content suggestions and customizable branding.

## Development Commands

### Local Development
```bash
# Start all services in development mode
npm run dev

# Start individual services
npm run dev:api      # API backend on port 3000
npm run dev:admin    # Admin panel on port 5173
npm run dev:embed    # Embed widget on port 5174

# Build all packages
npm run build

# Individual builds
npm run build:api
npm run build:admin
npm run build:embed
```

### Database Management
```bash
# Complete database setup (first time)
npm run db:setup

# Reset existing database
npm run db:reset

# Run migrations only
npm run db:migrate

# Seed database with demo data
npm run db:seed

# Test database connection
npm run db:test
```

### Testing and Linting
```bash
# Run tests across all packages
npm run test

# Lint all packages
npm run lint

# Type check individual packages
npm run type-check --workspace=@ai-bot/api
npm run type-check --workspace=@ai-bot/admin
npm run type-check --workspace=@ai-bot/embed
```

### AWS Deployment
```bash
# Deploy everything
npm run deploy:full

# Deploy components individually
npm run deploy:frontend  # Admin + embed to S3/CloudFront
npm run deploy:backend   # API to Lambda/API Gateway

# Quick updates (faster than full deployment)
npm run deploy:frontend:update
npm run deploy:backend:update

# Check deployment status
npm run status:all
npm run deploy:frontend:status
npm run deploy:backend:status
```

### Utilities
```bash
# Switch between local/deployed API endpoints
npm run switch:api [local|deployed|status]

# View Lambda logs
npm run logs:tail

# Connect to deployed backend
npm run backend:connect

# Update Lambda environment variables
npm run lambda:env
```

## Architecture

### Package Structure
- **packages/api/**: Express.js backend with PostgreSQL, deployed as AWS Lambda
- **packages/admin/**: React + Tailwind CSS admin panel, deployed to S3/CloudFront
- **packages/embed/**: React embed widget built as UMD bundle, deployed to S3/CloudFront

### Key Technologies
- **API**: Node.js, TypeScript, Express, PostgreSQL, Joi validation, bcryptjs
- **Admin**: React 18, TypeScript, Tailwind CSS, React Query, React Hook Form, Vite
- **Embed**: React 18, TypeScript, CSS Modules, Vite (UMD output)

### Database Schema
Core tables: `clients`, `scraped_pages`, `bot_suggestions`, `bot_responses`, `analytics`

### API Endpoints
- `POST /api/bot/ask` - Get AI response
- `GET /api/bot/suggestions/:id` - Get suggestions  
- `GET /api/bot/links/:id` - Get link previews
- Client management endpoints under `/api/clients`
- Analytics endpoints under `/api/analytics`

## Development Workflow

### Code Conventions
- TypeScript strict mode enabled across all packages
- ESLint configuration for consistent code style
- Tailwind CSS for admin panel styling
- CSS Modules for embed widget styling
- React 18+ with hooks and modern patterns

### Testing Approach
- Jest for API unit and integration tests
- Test files located alongside source code
- Database testing uses test-specific connection
- E2E testing for admin panel user flows

### Deployment Infrastructure
- **API**: AWS Lambda + API Gateway + RDS PostgreSQL
- **Frontend**: S3 + CloudFront CDN
- **Security**: CloudFront-only S3 access, API rate limiting, CORS configuration

## Important Notes

### Database
- PostgreSQL with connection pooling
- Migrations handled via `npm run db:migrate`
- Seed data available via `npm run db:seed`
- Local development uses containerized PostgreSQL

### Configuration
- Environment variables managed per package
- AWS configuration in `scripts/deployment/shared/config.sh`
- CORS origins configured for multiple environments
- API Gateway and Lambda settings in deployment scripts

### Scripts Organization
- All deployment scripts in `scripts/` directory with comprehensive documentation
- Use npm scripts rather than calling shell scripts directly
- Automatic AWS credential setup via `npm run set:credentials`

### Monorepo Management
- Uses npm workspaces for dependency management
- Shared TypeScript and ESLint configurations
- Concurrent development with `concurrently` package
- Cross-package type imports supported