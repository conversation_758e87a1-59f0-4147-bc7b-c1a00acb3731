/**
 * API service for the admin panel.
 * This module provides a centralized interface for all API communications,
 * including client management, content scraping, and analytics with proper error handling.
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  Client, 
  CreateClientRequest, 
  UpdateClientRequest,
  ScrapedPage,
  AddScrapedPageRequest,
  BotSuggestion,
  CreateSuggestionRequest,
  Analytics,
  AnalyticsStats,
  ApiResponse,
  PaginatedResponse
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add admin key to all requests
    this.api.interceptors.request.use((config) => {
      const adminKey = import.meta.env.VITE_ADMIN_API_KEY;
      if (adminKey) {
        config.headers['X-Admin-Key'] = adminKey;
      }
      return config;
    });

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('API Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Client Management
  async getClients(page: number = 1, limit: number = 20): Promise<PaginatedResponse<Client>> {
    const response: AxiosResponse<PaginatedResponse<Client>> = await this.api.get('/clients', {
      params: { page, limit }
    });
    return response.data;
  }

  async getClient(id: string): Promise<Client> {
    const response: AxiosResponse<Client> = await this.api.get(`/clients/${id}`);
    return response.data;
  }

  async createClient(data: CreateClientRequest): Promise<Client> {
    const response: AxiosResponse<Client> = await this.api.post('/clients', data);
    return response.data;
  }

  async updateClient(id: string, data: UpdateClientRequest): Promise<Client> {
    const response: AxiosResponse<Client> = await this.api.put(`/clients/${id}`, data);
    return response.data;
  }

  async deleteClient(id: string): Promise<void> {
    await this.api.delete(`/clients/${id}`);
  }

  async regenerateApiKey(id: string): Promise<{ apiKey: string }> {
    const response: AxiosResponse<{ apiKey: string }> = await this.api.post(`/clients/${id}/regenerate-key`);
    return response.data;
  }

  // Scraped Pages Management
  async getScrapedPages(clientId: string): Promise<ScrapedPage[]> {
    const response: AxiosResponse<{ pages: ScrapedPage[] }> = await this.api.get(`/clients/${clientId}/pages`);
    return response.data.pages;
  }

  async addScrapedPage(clientId: string, data: AddScrapedPageRequest): Promise<ScrapedPage> {
    const response: AxiosResponse<ScrapedPage> = await this.api.post(`/clients/${clientId}/pages`, data);
    return response.data;
  }

  async updateScrapedPage(clientId: string, pageId: string, data: Partial<AddScrapedPageRequest>): Promise<ScrapedPage> {
    const response: AxiosResponse<ScrapedPage> = await this.api.put(`/clients/${clientId}/pages/${pageId}`, data);
    return response.data;
  }

  async deleteScrapedPage(clientId: string, pageId: string): Promise<void> {
    await this.api.delete(`/clients/${clientId}/pages/${pageId}`);
  }

  async scrapeUrl(clientId: string, url: string): Promise<ScrapedPage> {
    const response: AxiosResponse<ScrapedPage> = await this.api.post(`/clients/${clientId}/scrape`, { url });
    return response.data;
  }

  // Bot Suggestions Management
  async getSuggestions(clientId: string): Promise<BotSuggestion[]> {
    const response: AxiosResponse<{ suggestions: BotSuggestion[] }> = await this.api.get(`/clients/${clientId}/suggestions`);
    return response.data.suggestions;
  }

  async createSuggestion(clientId: string, data: CreateSuggestionRequest): Promise<BotSuggestion> {
    const response: AxiosResponse<BotSuggestion> = await this.api.post(`/clients/${clientId}/suggestions`, data);
    return response.data;
  }

  async updateSuggestion(clientId: string, suggestionId: string, data: Partial<CreateSuggestionRequest>): Promise<BotSuggestion> {
    const response: AxiosResponse<BotSuggestion> = await this.api.put(`/clients/${clientId}/suggestions/${suggestionId}`, data);
    return response.data;
  }

  async deleteSuggestion(clientId: string, suggestionId: string): Promise<void> {
    await this.api.delete(`/clients/${clientId}/suggestions/${suggestionId}`);
  }

  // Analytics
  async getAnalytics(clientId: string, startDate?: string, endDate?: string): Promise<AnalyticsStats> {
    const response: AxiosResponse<AnalyticsStats> = await this.api.get(`/analytics/${clientId}`, {
      params: { startDate, endDate }
    });
    return response.data;
  }

  async getAnalyticsData(clientId: string, page: number = 1, limit: number = 50): Promise<PaginatedResponse<Analytics>> {
    const response: AxiosResponse<PaginatedResponse<Analytics>> = await this.api.get(`/analytics/${clientId}/data`, {
      params: { page, limit }
    });
    return response.data;
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; database: string; timestamp: string }> {
    const response: AxiosResponse<{ status: string; database: string; timestamp: string }> = await this.api.get('/health');
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
