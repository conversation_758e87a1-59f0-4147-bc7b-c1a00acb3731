# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_answer_bot
DB_USER=ai_answer_bot_user
DB_PASSWORD=password

# Server Configuration
PORT=5000
NODE_ENV=development

# Security
ADMIN_API_KEY=dev-admin-key-1755295719
JWT_SECRET=e514df587ed17d79b9f8fb686485b130a26232a249c3aa7b39885f05b5fb6780

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:5000,http://localhost:5001,http://localhost:5175,http://localhost:5173,http://localhost:5174,http://localhost:5175

# Development flags
DEBUG=true

# AWS Configuration (for deployment)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# Lambda Configuration
LAMBDA_FUNCTION_NAME=ai-answer-bot-api
API_GATEWAY_NAME=ai-answer-bot-gateway

# RDS Configuration (for production)
RDS_INSTANCE_IDENTIFIER=ai-answer-bot-db
RDS_DB_NAME=ai_answer_bot
RDS_USERNAME=postgres
RDS_PASSWORD=your-secure-rds-password

# S3 Configuration (for deployment)
S3_BUCKET_NAME=ai-answer-bot-frontend
CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id

# Rate Limiting
RATE_LIMIT_POINTS=100
RATE_LIMIT_DURATION=60

# Logging
LOG_LEVEL=debug
