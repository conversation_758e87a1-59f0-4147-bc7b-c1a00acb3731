# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_answer_bot
DB_USER=postgres
DB_PASSWORD=password

# Server Configuration
PORT=3000
NODE_ENV=development

# Security
ADMIN_API_KEY=your-super-secret-admin-key-here
JWT_SECRET=your-jwt-secret-here

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://yourdomain.com

# AWS Configuration (for deployment)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key

# Lambda Configuration
LAMBDA_FUNCTION_NAME=ai-answer-bot-api
API_GATEWAY_NAME=ai-answer-bot-gateway

# RDS Configuration
RDS_INSTANCE_IDENTIFIER=ai-answer-bot-db
RDS_DB_NAME=ai_answer_bot
RDS_USERNAME=postgres
RDS_PASSWORD=your-secure-password

# S3 Configuration
S3_BUCKET_NAME=ai-answer-bot-frontend
CLOUDFRONT_DISTRIBUTION_ID=your-distribution-id
